# Deployment Script Updates

## 🎯 Overview

The deployment script (`scripts/deploy.sh`) has been updated to support the new project structure and automatically install all required Ubuntu packages for Puppeteer/Chrome functionality.

## ✅ What's New

### 1. **Puppeteer Dependencies Installation**

Added a new function `install_puppeteer_dependencies()` that automatically installs all required Ubuntu packages:

```bash
ca-certificates fonts-liberation libasound2 libatk-bridge2.0-0 libatk1.0-0
libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgbm1
libgcc1 libglib2.0-0 libgtk-3-0 libnspr4 libnss3 libpango-1.0-0
libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1
libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6
libxrandr2 libxrender1 libxss1 libxtst6 lsb-release wget xdg-utils
```

### 2. **Updated for New Project Structure**

- **Entry Point**: Updated to use `app/index.js` instead of `app.js`
- **Docker Paths**: Updated to use `deployment/docker/` directory
- **PM2 Config**: Updated to use `deployment/ecosystem.config.js`
- **Config Examples**: Updated to use `config/examples/` directory

### 3. **Enhanced Setup Process**

The `setup` command now includes:
1. Basic system dependencies
2. **🆕 Puppeteer/Chrome dependencies**
3. Node.js installation
4. Docker installation
5. User creation

## 🚀 Usage

### Initial Server Setup

```bash
# Transfer files to server
rsync -avz --exclude node_modules --exclude .git . user@your-server:/usr/src/app/

# SSH to server
ssh user@your-server
cd /usr/src/app

# Run complete setup (includes all Puppeteer dependencies)
sudo ./scripts/deploy.sh setup
```

### Deploy Application

```bash
# Docker deployment
sudo ./scripts/deploy.sh docker

# OR Native deployment with PM2
sudo ./scripts/deploy.sh native
```

### Update Application

```bash
# Update existing deployment
sudo ./scripts/deploy.sh update
```

## 🔧 What Gets Installed

### Basic Dependencies
- curl, wget, git
- build-essential (gcc, make, etc.)
- python3, python3-pip
- ca-certificates, gnupg, lsb-release

### Node.js Environment
- Node.js 18.x
- npm (latest)

### Docker Environment
- Docker CE
- Docker Compose
- Docker daemon configuration

### Puppeteer/Chrome Dependencies
All required libraries for headless Chrome:
- Audio libraries (libasound2)
- Graphics libraries (libcairo2, libx11-*, etc.)
- Font libraries (fonts-liberation, libfontconfig1)
- GTK libraries (libgtk-3-0, libatk-*)
- System libraries (libc6, libgcc1, libstdc++6)

## 📋 Deployment Modes

### 1. Setup Mode
```bash
sudo ./scripts/deploy.sh setup
```
- Installs all system dependencies
- **🆕 Installs Puppeteer dependencies**
- Sets up Node.js and Docker
- Creates application user

### 2. Docker Mode
```bash
sudo ./scripts/deploy.sh docker
```
- Clones/updates repository
- Sets up configuration files
- Builds and starts Docker containers
- Performs health check

### 3. Native Mode
```bash
sudo ./scripts/deploy.sh native
```
- Clones/updates repository
- Installs npm dependencies
- Sets up configuration files
- Starts with PM2
- Performs health check

### 4. Update Mode
```bash
sudo ./scripts/deploy.sh update
```
- Creates backup
- Updates code
- Restarts application
- Performs health check

## 🔍 Health Checks

The script now properly checks for the new entry point:
- Looks for `node.*app/index.js` process
- Verifies application is running correctly

## 📁 File Structure Updates

The script now handles the new project structure:

```bash
# Old paths → New paths
app.js → app/index.js
ecosystem.config.js → deployment/ecosystem.config.js
Dockerfile → deployment/docker/Dockerfile
docker-compose.yml → deployment/docker/docker-compose.yml
proxy.config.example.js → config/examples/proxy.config.example.js
captcha.config.example.js → config/examples/captcha.config.example.js
```

## ✅ Benefits

1. **🚀 One-Command Setup**: Single command installs everything needed
2. **🔧 No Manual Package Installation**: All Puppeteer dependencies automated
3. **📦 Updated Structure Support**: Works with new organized project structure
4. **🐳 Docker Ready**: Proper Docker configuration paths
5. **⚡ PM2 Ready**: Updated PM2 configuration paths
6. **🔄 Easy Updates**: Streamlined update process

## 🎯 Next Steps

After running the deployment script:

1. **Configure Environment**: Edit `.env` file with your settings
2. **Set Slack Webhook**: Add your Slack webhook URL
3. **Test Configuration**: Run `npm run test-config`
4. **Start Crawling**: Application will start automatically

The deployment script now provides a complete, automated setup process that handles all the complex dependency management for you!
